# Đớp Phim - Movie Ticket Booking App

A comprehensive Flutter application for movie ticket booking with advanced features including real-time notifications, role-based authentication, and integrated payment processing.

## 🎬 Features

### Core Functionality
- **Movie Discovery**: Browse movies with detailed information, trailers, and ratings
- **Ticket Booking**: Complete booking system with seat selection and real-time availability
- **User Authentication**: Firebase-based authentication with Google Sign-In support
- **Role-Based Access**: Different access levels for users, admins, and developers
- **Payment Integration**: PayPal sandbox integration for secure payments
- **Real-time Notifications**: Live notification system for bookings and updates

### Advanced Features
- **Schedule Management**: Comprehensive movie scheduling with theaters and screens
- **Admin Dashboard**: Theater, screen, and movie management for administrators
- **Bulk Import**: Excel/CSV import functionality for movies, theaters, and schedules
- **QR Code Tickets**: Digital tickets with QR codes for easy verification
- **Automatic Expiration**: Server-side ticket expiration handling
- **Concurrent Booking Protection**: Prevents double-booking conflicts
- **Error Reporting**: Built-in error reporting system with admin notifications

### User Experience
- **Splash Screen**: Auto-scrolling movie banners on app launch
- **Genre Navigation**: Vietnamese UI with English backend integration
- **Search Functionality**: Search movies by title with optimized performance
- **Favorite Movies**: Personal movie collection with reload functionality
- **Profile Management**: Avatar upload to Firebase Storage with display name changes
- **Help System**: Direct support chat and ticket submission system

## 🛠 Technology Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Realtime Database, Authentication, Storage, Functions)
- **Payment**: PayPal SDK
- **Video Player**: Media Kit for optimized trailer playback
- **State Management**: GetX
- **UI Components**: Material Design with custom theming

## 📱 Screenshots

### Home Page
<img src="assets/project_progress/279225706_552121216268632_8462016636129319895_n.png" width=270 >

### Movie Detail Page
<img src="assets/project_progress/280654967_1984518965082018_8662307702767337035_n.png" width=270 >

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (>=2.16.2 <3.0.0)
- Android Studio / VS Code
- Firebase project setup
- PayPal developer account (for payment testing)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/movie-finder-flutter.git
   cd movie-finder-flutter
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project
   - Enable Authentication, Firestore, Realtime Database, Storage, and Functions
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place configuration files in appropriate directories
   - Update Firebase security rules using provided rule files

4. **Configure Firebase Functions**
   ```bash
   cd functions
   npm install
   firebase deploy --only functions
   ```

5. **Set up PayPal**
   - Create PayPal developer account
   - Configure sandbox credentials
   - Update payment configuration in the app

6. **Run the application**
   ```bash
   flutter run
   ```

## 🔧 Configuration

### Firebase Security Rules
The project includes pre-configured security rules for:
- Firestore (`firestore.rules`)
- Realtime Database (`database.rules.json`)
- Firebase Storage (`storage.rules`)

Deploy rules using:
```bash
firebase deploy --only firestore:rules,database,storage
```

### Environment Variables
Create appropriate configuration files for:
- API keys
- Firebase configuration
- PayPal credentials
- Other sensitive data

## 📚 Project Structure

```
lib/
├── bindings/          # GetX bindings
├── config/           # App configuration
├── controllers/      # GetX controllers
├── models/          # Data models
├── services/        # API and Firebase services
├── utils/           # Utility functions
├── view/            # UI screens
├── widgets/         # Reusable widgets
└── translations/    # Internationalization
```

## 🔐 Security Features

- Firebase Authentication with role-based access control
- Secure payment processing through PayPal
- Data validation and sanitization
- Protected admin routes and functions
- Encrypted sensitive data storage

## 🧪 Testing

Run tests using:
```bash
flutter test
```

For specific test files:
```bash
flutter test test/ticket_detail_test.dart
```

## 📖 Documentation

Additional documentation available:
- [Firebase Setup Guide](FIREBASE_STORAGE_AVATAR_README.md)
- [PayPal Integration Guide](PAYPAL_INTEGRATION_GUIDE.md)
- [Security Rules Guide](SECURITY_RULES_GUIDE.md)
- [Import Functionality Guide](IMPORT_GUIDE.md)
- [Notification System Guide](REALTIME_NOTIFICATION_SYSTEM.md)

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Firebase for backend services
- PayPal for payment integration
- All contributors and testers

## 📞 Support

For support and questions:
- Create an issue in this repository
- Use the in-app help system
- Contact the development team

---

**Note**: This application is designed for educational and demonstration purposes. Ensure proper security measures and compliance with local regulations before deploying to production.
