import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/notification_settings_controller.dart';
import '../../utils/app_colors.dart';

class NotificationSettingsPage extends StatelessWidget {
  const NotificationSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(NotificationSettingsController());

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradientVertical,
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Custom header thay thế AppBar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Cài đặt thông báo',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    // Save button
                    Obx(() => IconButton(
                          icon: controller.isSaving
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Icon(Icons.save, color: Colors.white),
                          onPressed: controller.isSaving
                              ? null
                              : () => controller.saveSettings(),
                        )),
                    // Reset button
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert, color: Colors.white),
                      onSelected: (value) {
                        switch (value) {
                          case 'reset':
                            _showResetDialog(context, controller);
                            break;
                          case 'export':
                            _exportSettings(controller);
                            break;
                          case 'import':
                            _showImportDialog(context, controller);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'reset',
                          child: Row(
                            children: [
                              Icon(Icons.restore),
                              SizedBox(width: 8),
                              Text('Khôi phục mặc định'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'export',
                          child: Row(
                            children: [
                              Icon(Icons.download),
                              SizedBox(width: 8),
                              Text('Xuất cài đặt'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'import',
                          child: Row(
                            children: [
                              Icon(Icons.upload),
                              SizedBox(width: 8),
                              Text('Nhập cài đặt'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Body content
              Expanded(
                child: Obx(() {
                  if (controller.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  if (controller.settings == null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.settings_outlined,
                            size: 64,
                            color: Colors.white,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Không thể tải cài đặt thông báo',
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                          if (controller.errorMessage.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              controller.errorMessage,
                              style: GoogleFonts.mulish(
                                fontSize: 14,
                                color: AppColors.errorRed,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => controller.loadSettings(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryGradientStart,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Thử lại'),
                          ),
                        ],
                      ),
                    );
                  }

                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // General Settings
                        _buildSectionHeader('Cài đặt chung'),
                        _buildSwitchTile(
                          title: 'Bật thông báo đẩy',
                          subtitle: 'Nhận thông báo từ ứng dụng',
                          value: controller.settings!.enablePushNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'enablePushNotifications', value);
                          },
                        ),
                        _buildSwitchTile(
                          title: 'Âm thanh',
                          subtitle: 'Phát âm thanh khi có thông báo',
                          value: controller.settings!.enableSound,
                          onChanged: (value) {
                            controller.updateSetting('enableSound', value);
                          },
                        ),
                        _buildSwitchTile(
                          title: 'Rung',
                          subtitle: 'Rung thiết bị khi có thông báo',
                          value: controller.settings!.enableVibration,
                          onChanged: (value) {
                            controller.updateSetting('enableVibration', value);
                          },
                        ),

                        const SizedBox(height: 24),

                        // Notification Types
                        _buildSectionHeader('Loại thông báo'),
                        _buildSwitchTile(
                          title: 'Thông báo hệ thống',
                          subtitle: 'Cập nhật ứng dụng, bảo trì...',
                          value: controller.settings!.enableSystemNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'enableSystemNotifications', value);
                          },
                        ),
                        _buildSwitchTile(
                          title: 'Thông báo phim',
                          subtitle: 'Phim mới, trailer, đánh giá...',
                          value: controller.settings!.enableMovieNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'enableMovieNotifications', value);
                          },
                        ),
                        _buildSwitchTile(
                          title: 'Thông báo khuyến mãi',
                          subtitle: 'Ưu đãi, giảm giá vé...',
                          value: controller.settings!.enablePromoNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'enablePromoNotifications', value);
                          },
                        ),
                        _buildSwitchTile(
                          title: 'Thông báo vé',
                          subtitle: 'Xác nhận đặt vé, nhắc nhở...',
                          value: controller.settings!.enableTicketNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'enableTicketNotifications', value);
                          },
                        ),
                        _buildSwitchTile(
                          title: 'Thông báo báo lỗi',
                          subtitle: 'Phản hồi từ đội ngũ phát triển',
                          value:
                              controller.settings!.enableBugReportNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'enableBugReportNotifications', value);
                          },
                        ),

                        const SizedBox(height: 24),

                        // Advanced Settings
                        _buildSectionHeader('Cài đặt nâng cao'),
                        _buildSwitchTile(
                          title: 'Giờ yên lặng',
                          subtitle: controller.settings!.enableQuietHours
                              ? '${controller.settings!.quietHoursStart} - ${controller.settings!.quietHoursEnd}'
                              : 'Tắt thông báo trong khoảng thời gian nhất định',
                          value: controller.settings!.enableQuietHours,
                          onChanged: (value) {
                            controller.updateSetting('enableQuietHours', value);
                          },
                        ),

                        if (controller.settings!.enableQuietHours) ...[
                          _buildTimePickerTile(
                            title: 'Bắt đầu giờ yên lặng',
                            time: controller.settings!.quietHoursStart,
                            onTimeChanged: (time) {
                              controller.updateSetting('quietHoursStart', time);
                            },
                          ),
                          _buildTimePickerTile(
                            title: 'Kết thúc giờ yên lặng',
                            time: controller.settings!.quietHoursEnd,
                            onTimeChanged: (time) {
                              controller.updateSetting('quietHoursEnd', time);
                            },
                          ),
                        ],

                        _buildSwitchTile(
                          title: 'Nhóm thông báo tương tự',
                          subtitle: 'Gộp các thông báo cùng loại',
                          value: controller.settings!.groupSimilarNotifications,
                          onChanged: (value) {
                            controller.updateSetting(
                                'groupSimilarNotifications', value);
                          },
                        ),

                        _buildSliderTile(
                          title: 'Số thông báo tối đa mỗi ngày',
                          subtitle:
                              '${controller.settings!.maxNotificationsPerDay} thông báo',
                          value: controller.settings!.maxNotificationsPerDay
                              .toDouble(),
                          min: 10,
                          max: 100,
                          divisions: 9,
                          onChanged: (value) {
                            controller.updateSetting(
                                'maxNotificationsPerDay', value.round());
                          },
                        ),

                        const SizedBox(height: 24),

                        // Summary
                        _buildSectionHeader('Tóm tắt cài đặt'),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                                color: Colors.white.withOpacity(0.2)),
                          ),
                          child: Text(
                            controller.getPreferencesSummary(),
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.8),
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: GoogleFonts.mulish(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: SwitchListTile(
        title: Text(
          title,
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.mulish(
            fontSize: 12,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primaryGradientStart,
      ),
    );
  }

  Widget _buildTimePickerTile({
    required String title,
    required String time,
    required ValueChanged<String> onTimeChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        title: Text(
          title,
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        trailing: Text(
          time,
          style: GoogleFonts.mulish(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryGradientStart,
          ),
        ),
        onTap: () async {
          final timeParts = time.split(':');
          final initialTime = TimeOfDay(
            hour: int.parse(timeParts[0]),
            minute: int.parse(timeParts[1]),
          );

          final pickedTime = await showTimePicker(
            context: Get.context!,
            initialTime: initialTime,
          );

          if (pickedTime != null) {
            final formattedTime =
                "${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}";
            onTimeChanged(formattedTime);
          }
        },
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: GoogleFonts.mulish(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            Text(
              subtitle,
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
            Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              onChanged: onChanged,
              activeColor: AppColors.primaryGradientStart,
              inactiveColor: Colors.white.withOpacity(0.3),
            ),
          ],
        ),
      ),
    );
  }

  void _showResetDialog(
      BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Khôi phục mặc định',
          style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Bạn có chắc chắn muốn khôi phục tất cả cài đặt về mặc định?',
          style: GoogleFonts.mulish(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              controller.resetToDefaults();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('Khôi phục'),
          ),
        ],
      ),
    );
  }

  void _exportSettings(NotificationSettingsController controller) {
    final settings = controller.exportSettings();
    // TODO: Implement export functionality (save to file, share, etc.)
    Get.snackbar(
      'Xuất cài đặt',
      'Tính năng xuất cài đặt sẽ được triển khai sau',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showImportDialog(
      BuildContext context, NotificationSettingsController controller) {
    // TODO: Implement import functionality
    Get.snackbar(
      'Nhập cài đặt',
      'Tính năng nhập cài đặt sẽ được triển khai sau',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
