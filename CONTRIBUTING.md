# Contributing to <PERSON><PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON><PERSON> Phi<PERSON>! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

1. **Search existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce the problem
   - Expected vs actual behavior
   - Screenshots or error messages
   - Device/platform information
   - App version

### Suggesting Features

1. **Check existing feature requests** to avoid duplicates
2. **Describe the feature** clearly and provide use cases
3. **Explain the benefits** and potential impact
4. **Consider implementation complexity** and alternatives

### Code Contributions

#### Prerequisites

- Flutter SDK (>=2.16.2 <3.0.0)
- Git knowledge
- Understanding of Dart/Flutter development
- Firebase experience (preferred)

#### Development Setup

1. **Fork the repository**
2. **Clone your fork**
   ```bash
   git clone https://github.com/yourusername/movie-finder-flutter.git
   cd movie-finder-flutter
   ```
3. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
4. **Install dependencies**
   ```bash
   flutter pub get
   ```
5. **Set up Firebase** (see README.md for detailed instructions)

#### Coding Standards

##### Code Style
- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use meaningful variable and function names
- Add comments for complex logic
- Keep functions small and focused
- Use proper indentation (2 spaces)

##### Flutter Best Practices
- Use `const` constructors where possible
- Implement proper state management with GetX
- Follow Material Design guidelines
- Ensure responsive design for different screen sizes
- Optimize performance and memory usage

##### File Organization
- Place files in appropriate directories
- Use descriptive file names
- Group related functionality together
- Follow the existing project structure

#### Testing

1. **Write tests** for new features and bug fixes
2. **Run existing tests** to ensure no regressions
   ```bash
   flutter test
   ```
3. **Test on multiple devices** and screen sizes
4. **Verify Firebase integration** works correctly

#### Pull Request Process

1. **Update documentation** if needed
2. **Add/update tests** for your changes
3. **Ensure all tests pass**
4. **Follow the PR template**
5. **Request review** from maintainers

##### PR Guidelines
- **Clear title** describing the change
- **Detailed description** of what was changed and why
- **Link related issues** using keywords (fixes #123)
- **Include screenshots** for UI changes
- **Keep PRs focused** - one feature/fix per PR

## 🏗 Project Architecture

### Directory Structure
```
lib/
├── bindings/          # GetX dependency injection
├── config/           # App configuration and constants
├── controllers/      # GetX controllers for state management
├── models/          # Data models and entities
├── services/        # API services and Firebase integration
├── utils/           # Utility functions and helpers
├── view/            # UI screens and pages
├── widgets/         # Reusable UI components
└── translations/    # Internationalization files
```

### Key Technologies
- **State Management**: GetX
- **Backend**: Firebase (Firestore, Auth, Storage, Functions)
- **Payment**: PayPal SDK
- **Video**: Media Kit
- **UI**: Material Design with custom theming

## 🔒 Security Guidelines

### Sensitive Information
- **Never commit** API keys, passwords, or secrets
- **Use environment variables** for configuration
- **Follow Firebase security rules** best practices
- **Validate all user inputs** on both client and server

### Code Security
- **Sanitize data** before database operations
- **Implement proper authentication** checks
- **Use HTTPS** for all network requests
- **Follow OWASP mobile security guidelines**

## 🐛 Bug Reports

### Before Reporting
1. **Update to latest version**
2. **Check existing issues**
3. **Try to reproduce** the issue consistently

### Bug Report Template
```
**Bug Description**
A clear description of the bug.

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What you expected to happen.

**Actual Behavior**
What actually happened.

**Screenshots**
If applicable, add screenshots.

**Environment**
- Device: [e.g. iPhone 12, Samsung Galaxy S21]
- OS: [e.g. iOS 15.0, Android 11]
- App Version: [e.g. 1.0.0]
- Flutter Version: [e.g. 3.0.0]
```

## 📝 Documentation

### Code Documentation
- **Document public APIs** with clear descriptions
- **Include examples** for complex functions
- **Update README.md** for significant changes
- **Maintain inline comments** for complex logic

### User Documentation
- **Update user guides** for new features
- **Include screenshots** for UI changes
- **Provide setup instructions** for new dependencies
- **Maintain troubleshooting guides**

## 🎯 Feature Development

### Planning Phase
1. **Discuss the feature** in an issue first
2. **Get approval** from maintainers
3. **Plan the implementation** approach
4. **Consider impact** on existing features

### Implementation Phase
1. **Create feature branch** from main
2. **Implement incrementally** with regular commits
3. **Test thoroughly** during development
4. **Update documentation** as needed

### Review Phase
1. **Self-review** your code before submitting
2. **Address feedback** promptly and professionally
3. **Make requested changes** in additional commits
4. **Squash commits** if requested

## 🚀 Release Process

### Version Numbering
- Follow [Semantic Versioning](https://semver.org/)
- **Major**: Breaking changes
- **Minor**: New features (backward compatible)
- **Patch**: Bug fixes (backward compatible)

### Release Checklist
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Version number bumped
- [ ] Changelog updated
- [ ] Firebase rules deployed
- [ ] App tested on multiple devices

## 📞 Getting Help

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Code Reviews**: Pull request discussions

### Response Times
- **Issues**: We aim to respond within 48 hours
- **Pull Requests**: Initial review within 72 hours
- **Security Issues**: Immediate attention (email maintainers)

## 📜 Code of Conduct

### Our Standards
- **Be respectful** and inclusive
- **Provide constructive feedback**
- **Focus on the code**, not the person
- **Help others learn** and grow
- **Follow project guidelines**

### Unacceptable Behavior
- Harassment or discrimination
- Trolling or insulting comments
- Publishing private information
- Spam or off-topic discussions

## 🙏 Recognition

Contributors will be recognized in:
- **README.md** contributors section
- **Release notes** for significant contributions
- **GitHub contributors** page

Thank you for contributing to Đớp Phim! 🎬
