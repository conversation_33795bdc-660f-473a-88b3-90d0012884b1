---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''
---

# 🐛 Bug Report

## 📝 Bug Description

<!-- A clear and concise description of what the bug is -->

## 🔄 Steps to Reproduce

1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

## ✅ Expected Behavior

<!-- A clear and concise description of what you expected to happen -->

## ❌ Actual Behavior

<!-- A clear and concise description of what actually happened -->

## 📱 Environment

### Device Information
- **Device**: [e.g. iPhone 12, Samsung Galaxy S21, Pixel 6]
- **OS**: [e.g. iOS 15.0, Android 11, Android 12]
- **Screen Size**: [e.g. 6.1", 6.2"]
- **Screen Resolution**: [e.g. 1170x2532, 1080x2400]

### App Information
- **App Version**: [e.g. 1.0.0]
- **Build Number**: [e.g. 1]
- **Installation Method**: [e.g. APK, Play Store, TestFlight]

### Development Environment (if applicable)
- **Flutter Version**: [e.g. 3.16.0]
- **Dart Version**: [e.g. 3.2.0]
- **IDE**: [e.g. VS Code, Android Studio]
- **OS**: [e.g. Windows 11, macOS 13, Ubuntu 22.04]

## 📸 Screenshots/Videos

<!-- If applicable, add screenshots or videos to help explain your problem -->

### Error Screenshot
<!-- Drag and drop your screenshot here -->

### Console Output
```
<!-- Paste any relevant console output or error messages here -->
```

## 🔍 Additional Context

### Frequency
- [ ] Happens every time
- [ ] Happens sometimes
- [ ] Happened once
- [ ] Happens under specific conditions

### User Account Type
- [ ] Regular user
- [ ] Admin user
- [ ] Developer account
- [ ] Guest/unauthenticated

### Network Conditions
- [ ] WiFi
- [ ] Mobile data (4G/5G)
- [ ] Poor connection
- [ ] Offline

### Related Features
<!-- Check all that apply -->
- [ ] Authentication/Login
- [ ] Movie browsing
- [ ] Ticket booking
- [ ] Payment processing
- [ ] Notifications
- [ ] Profile management
- [ ] Admin features
- [ ] Search functionality
- [ ] Video playback
- [ ] Firebase integration

## 🔧 Attempted Solutions

<!-- Describe any solutions you've tried -->

- [ ] Restarted the app
- [ ] Cleared app cache/data
- [ ] Reinstalled the app
- [ ] Tried on different device
- [ ] Checked internet connection
- [ ] Updated to latest version

## 📋 Logs

### Flutter Logs
```
<!-- Paste Flutter logs here if available -->
```

### Firebase Logs
```
<!-- Paste Firebase-related logs here if available -->
```

### Crash Logs
```
<!-- Paste crash logs here if available -->
```

## 🎯 Impact

### Severity
- [ ] Critical (app crashes, data loss)
- [ ] High (major feature broken)
- [ ] Medium (feature partially works)
- [ ] Low (minor issue, workaround available)

### User Impact
- [ ] Blocks core functionality
- [ ] Affects user experience
- [ ] Minor inconvenience
- [ ] Cosmetic issue

## 🔗 Related Issues

<!-- Link any related issues -->
- Related to #
- Duplicate of #
- Blocks #

## 📞 Contact Information

<!-- Optional: How can we reach you for more information? -->
- **GitHub**: @username
- **Email**: (if comfortable sharing)

---

## ✅ Checklist

Before submitting this issue, please confirm:

- [ ] I have searched for existing issues
- [ ] I have provided all required information
- [ ] I have tested on the latest version
- [ ] I have included relevant screenshots/logs
- [ ] I have described the steps to reproduce clearly

Thank you for helping us improve Đớp Phim! 🎬
